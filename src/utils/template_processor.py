"""
Template processing utility for dynamic input values.

This module provides functionality to process template strings in input values,
allowing dynamic substitution of event data fields using {json.field} syntax.
"""

import re
import logging
from typing import Any, Dict, List, Optional, Set, Tuple

logger = logging.getLogger(__name__)


class TemplateProcessor:
    """
    Utility class for processing template strings in input values.

    Supports:
    - Static values (backward compatibility)
    - Single field references: {json.username}
    - Nested field references: {json.user.profile.name}
    - Multiple references: "User {json.username} from {json.department}"
    - Mixed content: "Hello {json.username}, your ID is {json.id}"
    """

    # Regex pattern to match {json.field.path} syntax
    TEMPLATE_PATTERN = re.compile(r"\{json\.([^}]+)\}")

    def __init__(self):
        """Initialize the template processor."""
        pass

    def is_template(self, value: Any) -> bool:
        """
        Check if a value contains template syntax.

        Args:
            value: Value to check for template patterns

        Returns:
            bool: True if value contains {json.field} patterns
        """
        if not isinstance(value, str):
            return False

        return bool(self.TEMPLATE_PATTERN.search(value))

    def extract_field_references(self, template: str) -> Set[str]:
        """
        Extract all field references from a template string.

        Args:
            template: Template string to analyze

        Returns:
            Set[str]: Set of field paths referenced in the template

        Example:
            >>> processor = TemplateProcessor()
            >>> processor.extract_field_references("User {json.username} from {json.department}")
            {'username', 'department'}
        """
        matches = self.TEMPLATE_PATTERN.findall(template)
        return set(matches)

    def process_template(
        self, template: str, event_data: Any, field_type: str = "string"
    ) -> Any:
        """
        Process a template string by substituting field references with actual values.

        Args:
            template: Template string containing {json.field} references
            event_data: Event data (dict, list, or other types) to extract values from
            field_type: Expected field type for final conversion

        Returns:
            Any: Processed value with substitutions made

        Example:
            >>> event_data = {"username": "john", "department": "finance"}
            >>> processor.process_template("User {json.username} from {json.department}", event_data)
            "User john from finance"
        """
        if not self.is_template(template):
            # Not a template, return as-is for backward compatibility
            # If field_type is "string" (default) and template is not a string,
            # only convert if it's actually a string template, otherwise preserve type
            if field_type == "string" and isinstance(template, str):
                return template
            elif field_type == "string" and not isinstance(template, str):
                # For non-string values with string field_type, preserve original type
                # This handles cases where numbers/booleans are passed directly
                return template
            else:
                # Apply type conversion for explicit non-string field types
                return self._convert_field_type(template, field_type)

        # Normalize event data to handle both [{...}] and {...} cases
        normalized_event_data = self._normalize_event_data(event_data)

        # Extract field references
        field_refs = self.extract_field_references(template)

        # Build substitution map
        substitutions = {}
        for field_path in field_refs:
            field_value = self._get_nested_field_value(
                normalized_event_data, field_path
            )

            # Convert None to empty string for string interpolation
            if field_value is None:
                substitutions[f"{{json.{field_path}}}"] = ""
            else:
                # Convert to string for substitution
                substitutions[f"{{json.{field_path}}}"] = str(field_value)

        # Perform substitutions
        result = template
        for placeholder, value in substitutions.items():
            result = result.replace(placeholder, value)

        # Handle special case: if template was a single field reference
        if (
            len(field_refs) == 1
            and template.strip() == f"{{json.{list(field_refs)[0]}}}"
        ):
            original_value = self._get_nested_field_value(
                normalized_event_data, list(field_refs)[0]
            )
            if original_value is None:
                return None
            # For single field references with default "string" field_type, preserve original type
            # For explicit field_type, convert accordingly
            if field_type == "string" and not isinstance(original_value, str):
                # Preserve original type for non-string values when field_type is default "string"
                return original_value
            else:
                return self._convert_field_type(original_value, field_type)

        # For multi-field templates or mixed content, convert final result
        return self._convert_field_type(result, field_type)

    def process_input_values(
        self, input_values: List[Dict[str, Any]], event_data: Any
    ) -> Dict[str, Any]:
        """
        Process a list of input values, handling both static and template values.

        Args:
            input_values: List of input value configurations
            event_data: Event data for template substitution (can be dict, list, or other types)

        Returns:
            Dict[str, Any]: Processed field values mapped by field name
        """
        extracted = {}

        if not input_values:
            return extracted

        # Normalize event data to handle both [{...}] and {...} cases
        normalized_event_data = self._normalize_event_data(event_data)

        for input_value in input_values:
            field_name = input_value.get("field_name")
            field_value = input_value.get("field_value")
            field_type = input_value.get("field_type", "string")

            if not field_name or field_value is None:
                continue

            try:
                # Process the field value (handles both templates and static values)
                processed_value = self.process_template(
                    field_value, normalized_event_data, field_type
                )
                extracted[field_name] = processed_value

                logger.debug(
                    f"Processed input value: {field_name} = {processed_value} "
                    f"(original: {field_value}, type: {field_type})"
                )

            except Exception as e:
                logger.warning(
                    f"Failed to process input value {field_name}: {e}. "
                    f"Using original value: {field_value}"
                )
                # Fallback to original value with type conversion
                extracted[field_name] = self._convert_field_type(
                    field_value, field_type
                )

        return extracted

    def _normalize_event_data(self, event_data: Any) -> Dict[str, Any]:
        """
        Normalize event data to handle both array and object formats.

        Args:
            event_data: Event data that can be:
                       - Dict: {...} (single event object)
                       - List: [{...}] (array with single event)
                       - List: [{...}, {...}] (array with multiple events - uses first)
                       - Other types: returns empty dict

        Returns:
            Dict[str, Any]: Normalized event data as a dictionary
        """
        if event_data is None:
            return {}

        # If it's already a dictionary, return as-is
        if isinstance(event_data, dict):
            return event_data

        # If it's a list, extract the first element if it exists and is a dict
        if isinstance(event_data, list):
            if len(event_data) > 0 and isinstance(event_data[0], dict):
                logger.debug(
                    f"Normalized array event data with {len(event_data)} items, using first item"
                )
                return event_data[0]
            else:
                logger.warning(
                    f"Event data is an empty array or first item is not a dict: {type(event_data[0]) if event_data else 'empty'}"
                )
                return {}

        # For any other type, log a warning and return empty dict
        logger.warning(
            f"Unexpected event data type: {type(event_data)}, returning empty dict"
        )
        return {}

    def _get_nested_field_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """
        Get value from nested dictionary using dot notation.

        Args:
            data: Dictionary to extract from
            field_path: Dot-separated path (e.g., 'user.profile.name')

        Returns:
            Any: Field value or None if not found
        """
        try:
            current = data
            for key in field_path.split("."):
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            return current
        except (KeyError, TypeError, AttributeError):
            return None

    def _convert_field_type(self, value: Any, field_type: str) -> Any:
        """
        Convert value to the expected field type.

        Args:
            value: Value to convert
            field_type: Target field type (string, number, boolean, array)

        Returns:
            Any: Converted value
        """
        if value is None:
            return None

        try:
            if field_type == "number":
                if isinstance(value, (str, int, float)):
                    return float(value) if "." in str(value) else int(float(value))
                return value
            elif field_type == "boolean":
                if isinstance(value, str):
                    return value.lower() in ("true", "1", "yes", "on")
                return bool(value)
            elif field_type == "array":
                if isinstance(value, str):
                    # Try to parse as comma-separated values
                    return [item.strip() for item in value.split(",") if item.strip()]
                elif isinstance(value, list):
                    return value
                else:
                    return [value]
            else:  # string or unknown type
                return str(value)
        except (ValueError, TypeError):
            # If conversion fails, return original value
            return value
