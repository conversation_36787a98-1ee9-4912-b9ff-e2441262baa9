"""
Trigger management API endpoints.

This module provides REST API endpoints for managing triggers including
CRUD operations and trigger lifecycle management.
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Request, status
from typing import List, Optional, Dict, Any
from uuid import UUID

from src.schemas.trigger import (
    TriggerCreate,
    TriggerUpdate,
    TriggerResponse,
    TriggerListResponse,
    TriggerToggleRequest,
    TriggerFilterRequest,
    TriggerStatsResponse,
    TriggerExecutionResponse,
    TriggerTypesResponse,
    TriggerTypeInfo,
)
from src.core.trigger_manager import TriggerManager
from src.api.middleware.auth import get_authenticated_user
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/triggers", tags=["Triggers"])


# Dependency to get trigger manager instance
def get_trigger_manager() -> TriggerManager:
    """Get trigger manager instance."""
    # Use the singleton instance from TriggerManager
    return TriggerManager.get_instance()


@router.get("/types", response_model=TriggerTypesResponse)
async def get_trigger_types(
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerTypesResponse:
    """
    Get information about all available trigger types.

    This endpoint is public and does not require authentication.
    It returns information about all supported trigger types including
    their configuration schemas and sample event data.

    Returns:
        TriggerTypesResponse: Information about all available trigger types

    Raises:
        HTTPException: If there's an error retrieving trigger types
    """
    try:
        # Get all registered adapter names from the trigger manager
        adapter_names = trigger_manager.list_adapters()

        trigger_types = []
        for adapter_name in adapter_names:
            # Get the adapter instance
            adapter = trigger_manager.get_adapter(adapter_name)
            if not adapter:
                continue

            # Get adapter information
            adapter_info = adapter.get_adapter_info()

            # Create trigger type info
            trigger_type = TriggerTypeInfo(
                trigger_type=adapter_info.get("trigger_type", adapter_name),
                name=adapter_info.get("name", adapter_name.replace("_", " ").title()),
                description=adapter_info.get(
                    "description", f"{adapter_name} trigger adapter"
                ),
                supported_event_types=adapter_info.get("supported_event_types", []),
                configuration_schema=adapter_info.get("configuration_schema", {}),
                sample_event_data=adapter_info.get("sample_event_data", {}),
                available_fields=adapter_info.get("available_fields", []),
            )
            trigger_types.append(trigger_type)

        return TriggerTypesResponse(
            trigger_types=trigger_types, total_count=len(trigger_types)
        )

    except Exception as e:
        logger.error("Error retrieving trigger types", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger types",
        )


@router.post(
    "/",
    response_model=TriggerResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new trigger",
    description="""
    Create a new trigger for workflow automation.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-api-key>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    **Example Request**:
    ```bash
    curl -X POST "http://localhost:8000/api/v1/triggers/" \\
         -H "Authorization: Bearer abc" \\
         -H "Content-Type: application/json" \\
         -d '{
           "user_id": "user123",
           "workflow_id": "workflow456",
           "trigger_type": "google_calendar",
           "trigger_name": "My Calendar Trigger",
           "trigger_config": {"calendar_id": "primary"},
           "event_types": ["created", "updated"]
         }'
    ```
    """,
    responses={
        201: {"description": "Trigger created successfully"},
        400: {"description": "Invalid request data"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"},
    },
)
async def create_trigger(
    trigger_data: TriggerCreate,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Create a new trigger.

    Args:
        trigger_data: Trigger creation data
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Created trigger details

    Raises:
        HTTPException: If trigger creation fails
    """

    # Extract user ID from authenticated user
    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User ID not available from authentication",
        )

    try:
        # Use the legacy method that matches the expected signature
        trigger_id = await trigger_manager.create_trigger_legacy(
            user_id=user_id,
            workflow_id=trigger_data.workflow_id,
            trigger_type=trigger_data.trigger_type,
            trigger_name=trigger_data.trigger_name,
            trigger_config=trigger_data.trigger_config,
            event_types=trigger_data.event_types,
        )

        if not trigger_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create trigger",
            )

        # Get the created trigger details
        triggers = await trigger_manager.get_triggers_for_user(user_id)
        created_trigger = next((t for t in triggers if t.id == trigger_id), None)

        if not created_trigger:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Trigger created but could not retrieve details",
            )

        return TriggerResponse.from_orm(created_trigger)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating trigger: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while creating trigger",
        )


@router.get(
    "/",
    response_model=TriggerListResponse,
    summary="List triggers",
    description="""
    List triggers with optional filtering and pagination.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-api-key>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    **Filtering**: If no filter parameters are provided, returns all triggers with pagination.
    - `user_id`: Filter by specific user (optional)
    - `workflow_id`: Filter by specific workflow (optional)
    - `trigger_type`: Filter by trigger type (optional)
    - `is_active`: Filter by active status (optional)

    **Example Requests**:
    ```bash
    # Get all triggers (paginated)
    curl -X GET "http://localhost:8000/api/v1/triggers/" \\
         -H "Authorization: Bearer abc"

    # Get triggers for specific user
    curl -X GET "http://localhost:8000/api/v1/triggers/?user_id=user123" \\
         -H "Authorization: Bearer abc"

    # Get triggers with pagination
    curl -X GET "http://localhost:8000/api/v1/triggers/?page=2&page_size=10" \\
         -H "Authorization: Bearer abc"
    ```
    """,
    responses={
        200: {"description": "List of triggers retrieved successfully"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"},
    },
)
async def list_triggers(
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    user_id: Optional[str] = Query(None, description="Filter by user ID (admin only)"),
    workflow_id: Optional[str] = Query(None, description="Filter by workflow ID"),
    trigger_type: Optional[str] = Query(None, description="Filter by trigger type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerListResponse:
    """
    List triggers with optional filtering and pagination.

    Args:
        current_user: Authenticated user information from bearer token
        user_id: Optional user ID filter (admin only, defaults to current user)
        workflow_id: Optional workflow ID filter
        trigger_type: Optional trigger type filter
        is_active: Optional active status filter
        page: Page number for pagination
        page_size: Number of items per page
        trigger_manager: Trigger manager instance

    Returns:
        TriggerListResponse: Paginated list of triggers
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # For security, only allow users to see their own triggers unless they're admin
        # For now, we'll restrict to the authenticated user's triggers only
        # TODO: Add admin role checking if needed
        filter_user_id = authenticated_user_id

        # Use the new list_triggers method with database-level pagination
        triggers, total_count = await trigger_manager.list_triggers(
            user_id=filter_user_id,
            workflow_id=workflow_id,
            trigger_type=trigger_type,
            is_active=is_active,
            page=page,
            page_size=page_size,
        )

        # Convert to response models
        trigger_responses = [TriggerResponse.from_orm(t) for t in triggers]

        # Calculate total pages
        total_pages = (total_count + page_size - 1) // page_size

        return TriggerListResponse(
            triggers=trigger_responses,
            total=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while listing triggers",
        )


@router.get("/{trigger_id}", response_model=TriggerResponse)
async def get_trigger(
    trigger_id: UUID,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Get a specific trigger by ID.

    Args:
        trigger_id: Trigger ID to retrieve
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Trigger details

    Raises:
        HTTPException: If trigger not found or access denied
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only access your own triggers",
            )

        return TriggerResponse.from_orm(trigger)

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger",
        )


@router.put("/{trigger_id}", response_model=TriggerResponse)
async def update_trigger(
    trigger_id: UUID,
    trigger_data: TriggerUpdate,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Update a trigger.

    Args:
        trigger_id: Trigger ID to update
        trigger_data: Updated trigger data
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Updated trigger details

    Raises:
        HTTPException: If trigger not found or update fails
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only update your own triggers",
            )

        # For now, we'll implement a simple update by recreating the trigger
        # In a full implementation, you'd have an update method in TriggerManager

        # This is a placeholder - implement actual update logic
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Trigger update not yet implemented",
        )

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while updating trigger",
        )


@router.delete("/{trigger_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_trigger(
    trigger_id: UUID,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> None:
    """
    Delete a trigger.

    Args:
        trigger_id: Trigger ID to delete
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Raises:
        HTTPException: If trigger not found or deletion fails
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only delete your own triggers",
            )

        # Delete the trigger
        success = await trigger_manager.remove_trigger(trigger_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete trigger",
            )

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while deleting trigger",
        )


@router.post("/{trigger_id}/toggle", response_model=TriggerResponse)
async def toggle_trigger(
    trigger_id: UUID,
    toggle_data: TriggerToggleRequest,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Enable or disable a trigger.

    Args:
        trigger_id: Trigger ID to toggle
        toggle_data: Toggle request data
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Updated trigger details

    Raises:
        HTTPException: If trigger not found or toggle fails
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only toggle your own triggers",
            )

        # Toggle the trigger
        success = await trigger_manager.toggle_trigger(
            trigger_id, toggle_data.is_active
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to toggle trigger",
            )

        # Get updated trigger details
        updated_trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not updated_trigger:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Trigger toggled but could not retrieve updated details",
            )

        action = "enabled" if toggle_data.is_active else "disabled"

        return TriggerResponse.from_orm(updated_trigger)

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while toggling trigger",
        )


@router.get("/{trigger_id}/executions", response_model=List[TriggerExecutionResponse])
async def get_trigger_executions(
    trigger_id: UUID,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of executions to return"
    ),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> List[TriggerExecutionResponse]:
    """
    Get execution history for a trigger.

    Args:
        trigger_id: Trigger ID to get executions for
        current_user: Authenticated user information from bearer token
        limit: Maximum number of executions to return
        trigger_manager: Trigger manager instance

    Returns:
        List[TriggerExecutionResponse]: List of trigger executions

    Raises:
        HTTPException: If trigger not found or access denied
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only view executions for your own triggers",
            )

        # Get execution history
        executions = await trigger_manager.get_execution_history(trigger_id, limit)

        return [
            TriggerExecutionResponse.from_orm(execution) for execution in executions
        ]

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger executions",
        )


# @router.get("/stats", response_model=TriggerStatsResponse)
# async def get_trigger_stats(
#     request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
# ) -> TriggerStatsResponse:
#     """
#     Get trigger statistics for the current user.

#     Args:
#         request: HTTP request for authentication
#         trigger_manager: Trigger manager instance

#     Returns:
#         TriggerStatsResponse: Trigger statistics
#     """
#     try:
#         # Get user's triggers
#         triggers = await trigger_manager.get_triggers_for_user(current_user)

#         # Calculate statistics
#         total_triggers = len(triggers)
#         active_triggers = len([t for t in triggers if t.is_active])
#         inactive_triggers = total_triggers - active_triggers

#         # Count by type
#         triggers_by_type = {}
#         for trigger in triggers:
#             trigger_type = trigger.trigger_type
#             triggers_by_type[trigger_type] = triggers_by_type.get(trigger_type, 0) + 1

#         # For now, return placeholder values for execution stats
#         # In a full implementation, you'd query the execution history
#         recent_executions = 0
#         success_rate = 100.0

#         return TriggerStatsResponse(
#             total_triggers=total_triggers,
#             active_triggers=active_triggers,
#             inactive_triggers=inactive_triggers,
#             triggers_by_type=triggers_by_type,
#             recent_executions=recent_executions,
#             success_rate=success_rate,
#         )

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(
#             f"Failed to get trigger stats for user {current_user}", error=str(e)
#         )
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="Internal server error while retrieving trigger statistics",
#         )
