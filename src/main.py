"""
Main application entry point for the Trigger Service.

This module initializes and configures the FastAPI application with all
necessary middleware, routes, and startup/shutdown events.
"""

import uvicorn
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIKeyHeader
from contextlib import asynccontextmanager

from src.utils.config import get_settings

import asyncio
from src.database.connection import init_database, close_database, get_async_session
from src.api.routes import (
    webhooks,
    health,
)
from src.api.routes.triggers import router as triggers_router
from src.api.routes.schedulers import router as schedulers_router
from src.api.routes.google_drive import (
    router as google_drive_router,
)
from src.api.middleware.error_handler import ErrorHandlerMiddleware
from src.api.middleware.correlation import CorrelationMiddleware
from src.core.scheduler_engine import SchedulerEngine
from src.core.workflow_executor import WorkflowExecutor
from src.utils.logger import get_logger

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.

    Args:
        app: FastAPI application instance
    """
    # Startup
    try:
        await init_database()

        # Load existing webhook subscriptions
        await load_existing_webhook_subscriptions()

        # Start scheduler engine in a background task
        app.state.scheduler_task = asyncio.create_task(run_scheduler_engine())
        logger.info("Scheduler engine background task started.")

        yield
    finally:
        # Shutdown
        if hasattr(app.state, "scheduler_task"):
            app.state.scheduler_task.cancel()
            logger.info("Scheduler engine background task cancelled.")
        await close_database()


async def run_scheduler_engine():
    """
    Background task to run the scheduler engine periodically.
    """
    from src.core.scheduler_service import get_scheduler_service

    try:
        # Get the scheduler service instance
        scheduler_service = await get_scheduler_service()

        # Start the service
        await scheduler_service.start()
        logger.info("Scheduler service started successfully")

        # Run the scheduler loop
        while True:
            try:
                await scheduler_service.run_scheduler_cycle()
                await asyncio.sleep(
                    30
                )  # Run every 30 seconds to catch minute-level schedules
            except Exception as e:
                logger.error(f"Error in scheduler cycle: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    except Exception as e:
        logger.error(f"Error in scheduler engine background task: {e}")
        # Fallback to simple implementation for backward compatibility
        await run_simple_scheduler_engine()


async def run_simple_scheduler_engine():
    """
    Fallback simple scheduler implementation for backward compatibility.
    """
    while True:
        try:
            async for db_session in get_async_session():
                # Use the old method name for backward compatibility
                from src.core.scheduler_manager import SchedulerManager

                scheduler_manager = SchedulerManager(db_session)

                # Get due schedulers and process them
                due_schedulers = await scheduler_manager.get_due_schedulers(limit=50)

                for scheduler in due_schedulers:
                    try:
                        # Mark as processing
                        await scheduler_manager.mark_scheduler_processing(scheduler.id)

                        # Execute workflow (simplified)
                        workflow_executor = WorkflowExecutor(db_session)
                        result = await workflow_executor.execute_workflow(
                            scheduler.workflow_id,
                            scheduler.user_id,
                            {"scheduler_id": scheduler.id},
                        )

                        # Update next run time
                        await scheduler_manager.update_scheduler_next_run(
                            scheduler.id, scheduler.schedule
                        )

                        logger.debug(f"Processed scheduler {scheduler.id}")

                    except Exception as e:
                        logger.error(f"Error processing scheduler {scheduler.id}: {e}")
                        # Reset processing status on error
                        await scheduler_manager.reset_scheduler_processing(scheduler.id)

                break  # Only process once per iteration
        except Exception as e:
            logger.error(f"Error in simple scheduler engine: {e}")
        await asyncio.sleep(30)


async def load_existing_webhook_subscriptions():
    """Load existing webhook subscriptions and register them with adapters."""
    try:
        import json
        import os
        from datetime import datetime, timezone
        from uuid import uuid4
        from src.adapters.base import TriggerEventType

        # Check if webhook subscription file exists
        subscription_file = "webhook_subscription.json"
        if not os.path.exists(subscription_file):
            return

        # Load subscription info
        with open(subscription_file, "r") as f:
            subscription_info = json.load(f)

        # Check if subscription is still valid
        expires_at = datetime.fromisoformat(
            subscription_info["expires_at"].replace("Z", "+00:00")
        )
        now = datetime.now(timezone.utc)

        if expires_at <= now:
            return

        # Get the Google Calendar adapter from the trigger manager
        from src.api.routes.webhooks import get_trigger_manager

        trigger_manager = get_trigger_manager()

        # Find the Google Calendar adapter
        google_calendar_adapter = None
        for adapter_name, adapter in trigger_manager.adapters.items():
            if adapter_name == "google_calendar":
                google_calendar_adapter = adapter
                break

        if not google_calendar_adapter:
            return

        # Register the channel with the adapter
        channel_id = subscription_info["channel_id"]
        resource_id = subscription_info["resource_id"]
        calendar_id = subscription_info["calendar_id"]
        expiration_ms = int(expires_at.timestamp() * 1000)

        google_calendar_adapter._active_channels[channel_id] = {
            "resource_id": resource_id,
            "expiration": expiration_ms,
            "trigger_id": uuid4(),  # Generate a dummy trigger ID
            "user_id": "test-user",  # Default user ID
            "calendar_id": calendar_id,
            "event_types": [
                TriggerEventType.CREATED,
                TriggerEventType.UPDATED,
                TriggerEventType.DELETED,
            ],
            "created_at": datetime.now(timezone.utc).timestamp(),
        }

    except Exception:
        pass


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured application instance
    """
    settings = get_settings()

    # Create FastAPI app with lifespan and security schemes
    app = FastAPI(
        title="Trigger Service",
        description="""
        A simplified trigger service for workflow automation.

        ## Features
        - **Google Calendar integration** with webhook support
        - **Google Drive integration** with service account and user modes
        - **Scheduler management** for time-based workflows
        - **Real-time webhook processing**
        - **Dynamic input values** with template processing using `{json.field}` syntax
        - **Secure credential storage**

        ## Template Processing
        The service supports dynamic input values that can reference event data:
        - **Static values**: Traditional static values (backward compatible)
        - **Dynamic references**: `{json.username}`, `{json.user.profile.name}`
        - **Mixed templates**: `"Hello {json.username} from {json.department}"`
        - **Type conversion**: Automatic conversion based on field_type

        See the [Template Processing Documentation](https://github.com/your-repo/docs/template_processing.md) for detailed usage examples.

        ## Authentication
        Use either Bearer token or API key authentication:
        - `Authorization: Bearer <token>`
        - `X-API-Key: <key>`
        """,
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan,
        debug=settings.debug,
        openapi_tags=[
            {"name": "triggers", "description": "Trigger management operations"},
            {"name": "webhooks", "description": "Webhook processing endpoints"},
            {"name": "Google Calendar", "description": "Google Calendar integration"},
            {
                "name": "google-drive-service-account",
                "description": "Google Drive service account subscriptions",
            },
            {"name": "health", "description": "Health checks and monitoring"},
            {"name": "schedulers", "description": "Scheduler management operations"},
        ],
    )

    # Define security schemes for Swagger UI
    from fastapi.openapi.utils import get_openapi

    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema

        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )

        # Add security schemes
        openapi_schema["components"]["securitySchemes"] = {
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "API Key",
                "description": "Enter your API key as a Bearer token (e.g., 'abc' for development)",
            },
            "ApiKeyAuth": {
                "type": "apiKey",
                "in": "header",
                "name": "X-API-Key",
                "description": "Enter your API key in the X-API-Key header (e.g., 'abc' for development)",
            },
        }

        # Add security requirements to protected endpoints
        for path_item in openapi_schema["paths"].values():
            for operation in path_item.values():
                if isinstance(operation, dict) and "tags" in operation:
                    # Apply different security based on endpoint tags
                    tags = operation.get("tags", [])

                    # Google Drive endpoints: API Key only
                    if "google-drive-service-account" in tags:
                        operation["security"] = [{"ApiKeyAuth": []}]

                    # Triggers and Schedulers: Both Bearer and API Key
                    elif (
                        any(
                            tag in tags
                            for tag in ["Triggers", "triggers", "schedulers"]
                        )
                        and "public" not in tags
                        and "webhooks" not in tags
                        and "health" not in tags
                    ):
                        operation["security"] = [{"BearerAuth": []}, {"ApiKeyAuth": []}]

                    # Webhooks and Health: No authentication (remain public)
                    # No security added for these endpoints

        app.openapi_schema = openapi_schema
        return app.openapi_schema

    app.openapi = custom_openapi

    # Add middleware in correct order (last added = first executed)
    # CORS middleware should be last (first to execute)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Allow all origins for testing
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )

    # Add exception handlers
    @app.exception_handler(404)
    async def not_found_handler(request: Request, exc):
        """Handle 404 errors with consistent format."""
        return JSONResponse(
            status_code=404,
            content={
                "error": {
                    "type": "NotFound",
                    "message": "The requested resource was not found",
                    "status_code": 404,
                }
            },
        )

    @app.exception_handler(500)
    async def internal_error_handler(request: Request, exc):
        """Handle 500 errors with consistent format."""

        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "type": "InternalServerError",
                    "message": "An internal server error occurred",
                    "status_code": 500,
                }
            },
        )

    # Register routers
    app.include_router(health.router)
    app.include_router(triggers_router)
    app.include_router(webhooks.router)
    # app.include_router(google_calendar.router)
    app.include_router(google_drive_router)
    app.include_router(schedulers_router)

    return app


def get_app() -> FastAPI:
    """
    Get the FastAPI application instance.

    This function creates the app on-demand to avoid loading settings
    at module import time.

    Returns:
        FastAPI: Application instance
    """
    return create_app()


# Create app instance for uvicorn when running as module
app = get_app()


def main() -> None:
    """Main entry point for the application."""
    settings = get_settings()
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )


if __name__ == "__main__":
    main()
