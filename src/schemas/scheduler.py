from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
from typing import Optional, Dict, Any, List, Union
import re
import pytz
from uuid import UUID

from pydantic import BaseModel, Field, model_validator, field_validator, ConfigDict


# ============================================================================
# SIMPLIFIED SCHEDULER SCHEMAS - Production Ready
# ============================================================================


class ScheduleFrequency(str, Enum):
    """Enumeration of supported schedule frequencies."""

    EVERY_MINUTE = "every_minute"
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    CUSTOM = "custom"


class SchedulerInputValue(BaseModel):
    """
    Schema for defining input values to pass to workflows when scheduler executes.

    Supports both static values and dynamic template strings:
    - Static values: "finance", 5, true
    - Template strings: "{json.username}", "Hello {json.username}", "User {json.username} from {json.department}"
    - Nested fields: "{json.user.profile.name}", "{json.event.start.dateTime}"

    Template strings use {json.field} syntax where 'field' can be:
    - Simple field: {json.username}
    - Nested field: {json.user.profile.name}
    - Multiple references: "User {json.username} from {json.department}"

    If a referenced field doesn't exist in the event data, it will be substituted with null/empty string.
    """

    field_name: str = Field(
        ...,
        description="Name of the field to pass to the workflow",
        examples=["user_id", "department", "priority_level", "greeting_message"],
    )
    field_value: Any = Field(
        ...,
        description=(
            "Value to pass for this field. Can be static value or template string. "
            "Template strings use {json.field} syntax for dynamic substitution from event data. "
            "Examples: 'finance' (static), '{json.username}' (dynamic), 'Hello {json.username}' (mixed)"
        ),
        examples=[
            "user_123",  # Static string
            "finance",  # Static string
            5,  # Static number
            "{json.username}",  # Dynamic field reference
            "Hello {json.username}",  # Mixed template
            "User {json.username} from {json.department}",  # Multiple references
            "{json.user.profile.name}",  # Nested field reference
        ],
    )
    field_type: Optional[str] = Field(
        default="string",
        description="Expected field type (string, number, boolean, array)",
        examples=["string", "number", "boolean", "array"],
    )


class SimplifiedSchedulerBase(BaseModel):
    """Base schema for simplified scheduler with flat frequency-based structure."""

    name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Human-readable name for the scheduler job",
        examples=["Weekly Report Email", "Daily Backup", "Monthly Invoice Generation"],
    )

    frequency: ScheduleFrequency = Field(
        ...,
        description="Schedule frequency - determines when the job runs",
        examples=["daily", "weekly", "monthly"],
    )

    time: Optional[str] = Field(
        None,
        description="Time in HH:mm format (24-hour) - required for daily/weekly/monthly schedules",
        examples=["10:30", "14:15", "09:00"],
    )

    days_of_week: Optional[List[str]] = Field(
        None,
        description="Array of weekday names - required for weekly schedules",
        examples=[
            ["Monday"],
            ["Monday", "Wednesday", "Friday"],
            ["Saturday", "Sunday"],
        ],
    )

    days_of_month: Optional[List[int]] = Field(
        None,
        description="Array of day numbers (1-31) - required for monthly schedules",
        examples=[[1], [1, 15], [1, 15, 30]],
    )

    cron_expression: Optional[str] = Field(
        None,
        description="Cron expression - required only for custom frequency",
        examples=["30 10 * * 1", "0 9 * * 1-5", "0 0 1 * *"],
    )

    timezone: str = Field(
        "UTC",
        description="Timezone in Olson format for schedule execution",
        examples=["UTC", "Asia/Kolkata", "America/New_York", "Europe/London"],
    )

    is_active: bool = Field(
        True, description="Whether the scheduler is currently active and will execute"
    )

    input_values: Optional[List[SchedulerInputValue]] = Field(
        None,
        description=(
            "Optional input values to pass to the workflow when scheduler executes. "
            "Supports both static values and dynamic template strings using {json.field} syntax."
        ),
        examples=[
            [
                {
                    "field_name": "department",
                    "field_value": "finance",  # Static value
                    "field_type": "string",
                },
                {
                    "field_name": "priority_level",
                    "field_value": 5,  # Static number
                    "field_type": "number",
                },
                {
                    "field_name": "user_name",
                    "field_value": "{json.username}",  # Dynamic field reference
                    "field_type": "string",
                },
                {
                    "field_name": "greeting",
                    "field_value": "Hello {json.username}",  # Mixed template
                    "field_type": "string",
                },
                {
                    "field_name": "user_email",
                    "field_value": "{json.user.profile.email}",  # Nested field
                    "field_type": "string",
                },
            ]
        ],
    )

    @field_validator("input_values", mode="before")
    @classmethod
    def convert_input_values_from_db(cls, v):
        """Convert input values from database format (list of dicts) to SchedulerInputValue objects."""
        if v is None:
            return v

        if isinstance(v, list):
            # If it's already a list of SchedulerInputValue objects, return as is
            if v and isinstance(v[0], SchedulerInputValue):
                return v

            # Convert from list of dicts to list of SchedulerInputValue objects
            return [
                SchedulerInputValue(**item) if isinstance(item, dict) else item
                for item in v
            ]

        return v

    @field_validator("time")
    @classmethod
    def validate_time_format(cls, v: Optional[str]) -> Optional[str]:
        """Validate time is in HH:mm format (24-hour)."""
        if v is None:
            return v

        if not re.match(r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$", v):
            raise ValueError(
                'Time must be in HH:mm format (24-hour), e.g., "10:30" or "14:15"'
            )

        return v

    @field_validator("days_of_week")
    @classmethod
    def validate_days_of_week(cls, v: Optional[List[str]]) -> Optional[List[str]]:
        """Validate days of week are valid weekday names."""
        if v is None:
            return v

        if not isinstance(v, list) or len(v) == 0:
            raise ValueError("days_of_week must be a non-empty array of weekday names")

        valid_days = {
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
        }

        normalized_days = []
        for day in v:
            if not isinstance(day, str):
                raise ValueError("Each day must be a string")

            day_lower = day.lower().strip()
            if day_lower not in valid_days:
                raise ValueError(
                    f'Invalid weekday: {day}. Must be one of: {", ".join(sorted(valid_days))}'
                )

            # Normalize to title case
            normalized_days.append(day_lower.title())

        # Remove duplicates while preserving order
        seen = set()
        unique_days = []
        for day in normalized_days:
            if day not in seen:
                seen.add(day)
                unique_days.append(day)

        return unique_days

    @field_validator("days_of_month")
    @classmethod
    def validate_days_of_month(cls, v: Optional[List[int]]) -> Optional[List[int]]:
        """Validate days of month are valid day numbers (1-31)."""
        if v is None:
            return v

        if not isinstance(v, list) or len(v) == 0:
            raise ValueError("days_of_month must be a non-empty array of day numbers")

        for day in v:
            if not isinstance(day, int):
                raise ValueError("Each day must be an integer")

            if day < 1 or day > 31:
                raise ValueError(f"Day {day} is invalid. Days must be between 1 and 31")

        # Remove duplicates and sort
        unique_days = sorted(list(set(v)))
        return unique_days

    @field_validator("cron_expression")
    @classmethod
    def validate_cron_expression(cls, v: Optional[str]) -> Optional[str]:
        """Validate cron expression format."""
        if v is None:
            return v

        if not isinstance(v, str):
            raise ValueError("cron_expression must be a string")

        v = v.strip()
        if not v:
            raise ValueError("cron_expression cannot be empty")

        # Basic cron validation - should have 5 parts (minute hour day month weekday)
        parts = v.split()
        if len(parts) != 5:
            raise ValueError(
                "Cron expression must have exactly 5 parts: minute hour day month weekday"
            )

        return v

    @field_validator("timezone")
    @classmethod
    def validate_timezone(cls, v: str) -> str:
        """Validate timezone is a valid Olson timezone."""
        if not isinstance(v, str):
            raise ValueError("timezone must be a string")

        v = v.strip()
        if not v:
            raise ValueError("timezone cannot be empty")

        try:
            pytz.timezone(v)
        except pytz.exceptions.UnknownTimeZoneError:
            raise ValueError(
                f'Invalid timezone: {v}. Must be a valid Olson timezone (e.g., "UTC", "Asia/Kolkata")'
            )

        return v

    @model_validator(mode="after")
    def validate_frequency_requirements(self):
        """Validate that required fields are provided based on frequency."""
        frequency = self.frequency

        if frequency == ScheduleFrequency.EVERY_MINUTE:
            # No additional fields required
            if self.time is not None:
                raise ValueError(
                    "time should not be specified for every_minute frequency"
                )
            if self.days_of_week is not None:
                raise ValueError(
                    "days_of_week should not be specified for every_minute frequency"
                )
            if self.days_of_month is not None:
                raise ValueError(
                    "days_of_month should not be specified for every_minute frequency"
                )
            if self.cron_expression is not None:
                raise ValueError(
                    "cron_expression should not be specified for every_minute frequency"
                )

        elif frequency == ScheduleFrequency.HOURLY:
            # No additional fields required
            if self.time is not None:
                raise ValueError("time should not be specified for hourly frequency")
            if self.days_of_week is not None:
                raise ValueError(
                    "days_of_week should not be specified for hourly frequency"
                )
            if self.days_of_month is not None:
                raise ValueError(
                    "days_of_month should not be specified for hourly frequency"
                )
            if self.cron_expression is not None:
                raise ValueError(
                    "cron_expression should not be specified for hourly frequency"
                )

        elif frequency == ScheduleFrequency.DAILY:
            # time is required
            if self.time is None:
                raise ValueError('time is required for daily frequency (e.g., "10:30")')
            if self.days_of_week is not None:
                raise ValueError(
                    "days_of_week should not be specified for daily frequency"
                )
            if self.days_of_month is not None:
                raise ValueError(
                    "days_of_month should not be specified for daily frequency"
                )
            if self.cron_expression is not None:
                raise ValueError(
                    "cron_expression should not be specified for daily frequency"
                )

        elif frequency == ScheduleFrequency.WEEKLY:
            # time and days_of_week are required
            if self.time is None:
                raise ValueError(
                    'time is required for weekly frequency (e.g., "10:30")'
                )
            if self.days_of_week is None or len(self.days_of_week) == 0:
                raise ValueError(
                    'days_of_week is required for weekly frequency (e.g., ["Monday"])'
                )
            if self.days_of_month is not None:
                raise ValueError(
                    "days_of_month should not be specified for weekly frequency"
                )
            if self.cron_expression is not None:
                raise ValueError(
                    "cron_expression should not be specified for weekly frequency"
                )

        elif frequency == ScheduleFrequency.MONTHLY:
            # time and days_of_month are required
            if self.time is None:
                raise ValueError(
                    'time is required for monthly frequency (e.g., "10:30")'
                )
            if self.days_of_month is None or len(self.days_of_month) == 0:
                raise ValueError(
                    "days_of_month is required for monthly frequency (e.g., [1, 15])"
                )
            if self.days_of_week is not None:
                raise ValueError(
                    "days_of_week should not be specified for monthly frequency"
                )
            if self.cron_expression is not None:
                raise ValueError(
                    "cron_expression should not be specified for monthly frequency"
                )

        elif frequency == ScheduleFrequency.CUSTOM:
            # cron_expression is required
            if self.cron_expression is None:
                raise ValueError(
                    'cron_expression is required for custom frequency (e.g., "30 10 * * 1")'
                )
            if self.time is not None:
                raise ValueError(
                    "time should not be specified for custom frequency (use cron_expression instead)"
                )
            if self.days_of_week is not None:
                raise ValueError(
                    "days_of_week should not be specified for custom frequency (use cron_expression instead)"
                )
            if self.days_of_month is not None:
                raise ValueError(
                    "days_of_month should not be specified for custom frequency (use cron_expression instead)"
                )

        return self


class SimplifiedSchedulerCreate(SimplifiedSchedulerBase):
    """Schema for creating a new simplified scheduler."""

    workflow_id: str = Field(
        ...,
        description="ID of the workflow to execute when the schedule triggers",
        examples=["wf_123456", "workflow-abc-def"],
    )

    user_id: Optional[str] = Field(
        None,
        description="ID of the user who owns this scheduler (populated by system if not provided)",
        examples=["user_123", "usr_abc123"],
    )

    scheduler_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Optional metadata for the scheduler",
        examples=[{"department": "finance", "priority": "high"}],
    )

    model_config = ConfigDict(
        json_schema_extra={
            "examples": [
                {
                    "name": "Weekly Report Email",
                    "frequency": "weekly",
                    "time": "10:30",
                    "days_of_week": ["Monday"],
                    "timezone": "Asia/Kolkata",
                    "is_active": True,
                    "workflow_id": "wf_weekly_report_123",
                },
                {
                    "name": "Daily Backup",
                    "frequency": "daily",
                    "time": "02:00",
                    "timezone": "UTC",
                    "is_active": True,
                    "workflow_id": "wf_backup_456",
                },
                {
                    "name": "Monthly Invoice",
                    "frequency": "monthly",
                    "time": "09:00",
                    "days_of_month": [1, 15],
                    "timezone": "America/New_York",
                    "is_active": True,
                    "workflow_id": "wf_invoice_789",
                },
                {
                    "name": "Custom Schedule",
                    "frequency": "custom",
                    "cron_expression": "30 10 * * 1",
                    "timezone": "Europe/London",
                    "is_active": True,
                    "workflow_id": "wf_custom_abc",
                },
                {
                    "name": "Custom Schedule with Input Values",
                    "frequency": "custom",
                    "cron_expression": "30 10 * * 1",
                    "timezone": "Europe/London",
                    "is_active": True,
                    "workflow_id": "wf_custom_abc",
                    "input_values": [
                        {
                            "field_name": "department",
                            "field_value": "finance",  # Static value
                            "field_type": "string",
                        },
                        {
                            "field_name": "priority_level",
                            "field_value": 5,  # Static number
                            "field_type": "number",
                        },
                        {
                            "field_name": "user_name",
                            "field_value": "{json.username}",  # Dynamic field
                            "field_type": "string",
                        },
                        {
                            "field_name": "notification_message",
                            "field_value": "Hello {json.username}, your task is ready!",  # Template with text
                            "field_type": "string",
                        },
                    ],
                },
            ]
        }
    )


class SimplifiedSchedulerUpdate(SimplifiedSchedulerBase):
    """Schema for updating an existing simplified scheduler."""

    name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=255,
        description="Human-readable name for the scheduler job",
    )

    frequency: Optional[ScheduleFrequency] = Field(
        None, description="Schedule frequency - determines when the job runs"
    )

    workflow_id: Optional[str] = Field(
        None, description="ID of the workflow to execute when the schedule triggers"
    )

    timezone: Optional[str] = Field(
        None, description="Timezone in Olson format for schedule execution"
    )

    is_active: Optional[bool] = Field(
        None, description="Whether the scheduler is currently active and will execute"
    )

    scheduler_metadata: Optional[Dict[str, Any]] = Field(
        None, description="Optional metadata for the scheduler"
    )

    @model_validator(mode="after")
    def validate_frequency_requirements_for_update(self):
        """Validate that required fields are provided based on frequency for updates."""
        # Only validate if frequency is being updated
        if self.frequency is not None:
            # Create a temporary object with all fields to validate
            temp_data = {
                "name": self.name or "temp",
                "frequency": self.frequency,
                "time": self.time,
                "days_of_week": self.days_of_week,
                "days_of_month": self.days_of_month,
                "cron_expression": self.cron_expression,
                "timezone": self.timezone or "UTC",
                "is_active": self.is_active if self.is_active is not None else True,
                "workflow_id": "temp",
            }

            # Use the base class validation
            SimplifiedSchedulerBase.model_validate(temp_data)

        return self


class SimplifiedSchedulerResponse(SimplifiedSchedulerBase):
    """Schema for simplified scheduler responses."""

    id: str = Field(
        ...,
        description="Unique identifier of the scheduler",
        examples=["sched_123456", "sch_abc-def-789"],
    )

    @field_validator("id", mode="before")
    @classmethod
    def convert_uuid_to_string(cls, v: Union[str, UUID]) -> str:
        """Convert UUID objects to strings for compatibility."""
        if isinstance(v, UUID):
            return str(v)
        return v

    workflow_id: str = Field(
        ..., description="ID of the workflow to execute when the schedule triggers"
    )

    user_id: str = Field(..., description="ID of the user who owns this scheduler")

    created_at: datetime = Field(
        ..., description="Timestamp when the scheduler was created"
    )

    updated_at: datetime = Field(
        ..., description="Timestamp when the scheduler was last updated"
    )

    last_run_at: Optional[datetime] = Field(
        None, description="Timestamp of the last successful execution"
    )

    next_run_at: Optional[datetime] = Field(
        None, description="Timestamp of the next scheduled execution"
    )

    scheduler_metadata: Optional[Dict[str, Any]] = Field(
        None, description="Optional metadata for the scheduler"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "examples": [
                {
                    "id": "sched_123456",
                    "name": "Weekly Report Email",
                    "frequency": "weekly",
                    "time": "10:30",
                    "days_of_week": ["Monday"],
                    "timezone": "Asia/Kolkata",
                    "is_active": True,
                    "workflow_id": "wf_weekly_report_123",
                    "user_id": "user_789",
                    "created_at": "2025-01-17T10:30:00Z",
                    "updated_at": "2025-01-17T10:30:00Z",
                    "last_run_at": "2025-01-16T10:30:00Z",
                    "next_run_at": "2025-01-23T10:30:00Z",
                    "scheduler_metadata": {"department": "finance"},
                    "input_values": [
                        {
                            "field_name": "report_type",
                            "field_value": "weekly_summary",
                            "field_type": "string",
                        }
                    ],
                }
            ]
        },
    )
