"""
Tests for template processor event data normalization.

This module tests the normalization functionality that handles both
array [{...}] and object {...} formats for event data.
"""

import pytest
from src.utils.template_processor import TemplateProcessor


class TestTemplateProcessorNormalization:
    """Test event data normalization in template processor."""

    def setup_method(self):
        """Set up test fixtures."""
        self.processor = TemplateProcessor()
        
        # Sample Google Calendar event (as it would come from the API)
        self.calendar_event_object = {
            'kind': 'calendar#event',
            'etag': '"3506689185605982"',
            'id': '22r8vjrehp6r8itc8qje2qvl7s',
            'status': 'confirmed',
            'htmlLink': 'https://www.google.com/calendar/event?eid=*******************************************************************************',
            'created': '2025-07-24T08:09:52.000Z',
            'updated': '2025-07-24T08:09:52.802Z',
            'summary': 'hello',
            'creator': {
                'email': '<EMAIL>',
                'self': True
            },
            'organizer': {
                'email': '<EMAIL>',
                'self': True
            },
            'start': {
                'dateTime': '2025-07-25T21:30:00+05:30',
                'timeZone': 'Asia/Kolkata'
            },
            'end': {
                'dateTime': '2025-07-25T22:30:00+05:30',
                'timeZone': 'Asia/Kolkata'
            },
            'iCalUID': '<EMAIL>',
            'sequence': 0,
            'reminders': {'useDefault': True},
            'eventType': 'default'
        }
        
        # Same event wrapped in an array (as it might come from some APIs)
        self.calendar_event_array = [self.calendar_event_object]

    def test_normalize_event_data_object(self):
        """Test normalization of event data that's already an object."""
        result = self.processor._normalize_event_data(self.calendar_event_object)
        assert result == self.calendar_event_object
        assert result['summary'] == 'hello'
        assert result['creator']['email'] == '<EMAIL>'

    def test_normalize_event_data_array(self):
        """Test normalization of event data that's an array with one object."""
        result = self.processor._normalize_event_data(self.calendar_event_array)
        assert result == self.calendar_event_object
        assert result['summary'] == 'hello'
        assert result['creator']['email'] == '<EMAIL>'

    def test_normalize_event_data_multiple_array(self):
        """Test normalization of event data that's an array with multiple objects (uses first)."""
        multiple_events = [
            self.calendar_event_object,
            {
                'summary': 'second event',
                'creator': {'email': '<EMAIL>'}
            }
        ]
        
        result = self.processor._normalize_event_data(multiple_events)
        assert result == self.calendar_event_object
        assert result['summary'] == 'hello'  # Should use first event

    def test_normalize_event_data_empty_array(self):
        """Test normalization of empty array."""
        result = self.processor._normalize_event_data([])
        assert result == {}

    def test_normalize_event_data_none(self):
        """Test normalization of None."""
        result = self.processor._normalize_event_data(None)
        assert result == {}

    def test_normalize_event_data_invalid_types(self):
        """Test normalization of invalid data types."""
        # String
        result = self.processor._normalize_event_data("invalid")
        assert result == {}
        
        # Number
        result = self.processor._normalize_event_data(123)
        assert result == {}
        
        # Boolean
        result = self.processor._normalize_event_data(True)
        assert result == {}

    def test_normalize_event_data_array_with_non_dict(self):
        """Test normalization of array containing non-dict elements."""
        result = self.processor._normalize_event_data(["string", 123, True])
        assert result == {}

    def test_process_template_with_object_event_data(self):
        """Test template processing with object event data."""
        template = "Event '{json.summary}' by {json.creator.email}"
        result = self.processor.process_template(template, self.calendar_event_object)
        
        expected = "Event 'hello' by <EMAIL>"
        assert result == expected

    def test_process_template_with_array_event_data(self):
        """Test template processing with array event data."""
        template = "Event '{json.summary}' by {json.creator.email}"
        result = self.processor.process_template(template, self.calendar_event_array)
        
        expected = "Event 'hello' by <EMAIL>"
        assert result == expected

    def test_process_input_values_with_object_event_data(self):
        """Test input values processing with object event data."""
        input_values = [
            {
                "field_name": "event_title",
                "field_value": "{json.summary}",
                "field_type": "string"
            },
            {
                "field_name": "start_time",
                "field_value": "{json.start.dateTime}",
                "field_type": "string"
            },
            {
                "field_name": "creator_email",
                "field_value": "{json.creator.email}",
                "field_type": "string"
            },
            {
                "field_name": "notification",
                "field_value": "Meeting '{json.summary}' starts at {json.start.dateTime}",
                "field_type": "string"
            }
        ]
        
        result = self.processor.process_input_values(input_values, self.calendar_event_object)
        
        expected = {
            "event_title": "hello",
            "start_time": "2025-07-25T21:30:00+05:30",
            "creator_email": "<EMAIL>",
            "notification": "Meeting 'hello' starts at 2025-07-25T21:30:00+05:30"
        }
        assert result == expected

    def test_process_input_values_with_array_event_data(self):
        """Test input values processing with array event data."""
        input_values = [
            {
                "field_name": "event_title",
                "field_value": "{json.summary}",
                "field_type": "string"
            },
            {
                "field_name": "start_time",
                "field_value": "{json.start.dateTime}",
                "field_type": "string"
            },
            {
                "field_name": "creator_email",
                "field_value": "{json.creator.email}",
                "field_type": "string"
            },
            {
                "field_name": "notification",
                "field_value": "Meeting '{json.summary}' starts at {json.start.dateTime}",
                "field_type": "string"
            }
        ]
        
        result = self.processor.process_input_values(input_values, self.calendar_event_array)
        
        expected = {
            "event_title": "hello",
            "start_time": "2025-07-25T21:30:00+05:30",
            "creator_email": "<EMAIL>",
            "notification": "Meeting 'hello' starts at 2025-07-25T21:30:00+05:30"
        }
        assert result == expected

    def test_complex_nested_fields_with_both_formats(self):
        """Test complex nested field access with both object and array formats."""
        template = "{json.start.timeZone}"
        
        # Test with object
        result_object = self.processor.process_template(template, self.calendar_event_object)
        assert result_object == "Asia/Kolkata"
        
        # Test with array
        result_array = self.processor.process_template(template, self.calendar_event_array)
        assert result_array == "Asia/Kolkata"
        
        # Both should be identical
        assert result_object == result_array

    def test_missing_fields_with_both_formats(self):
        """Test handling of missing fields with both formats."""
        template = "User {json.nonexistent} from {json.missing.field}"
        
        # Test with object
        result_object = self.processor.process_template(template, self.calendar_event_object)
        expected = "User  from "
        assert result_object == expected
        
        # Test with array
        result_array = self.processor.process_template(template, self.calendar_event_array)
        assert result_array == expected
        
        # Both should be identical
        assert result_object == result_array

    def test_real_world_google_calendar_scenario(self):
        """Test a real-world scenario with Google Calendar event data."""
        # This simulates how the data might come from the Google Calendar adapter
        input_values = [
            {
                "field_name": "meeting_title",
                "field_value": "{json.summary}",
                "field_type": "string"
            },
            {
                "field_name": "meeting_url",
                "field_value": "{json.htmlLink}",
                "field_type": "string"
            },
            {
                "field_name": "organizer",
                "field_value": "{json.organizer.email}",
                "field_type": "string"
            },
            {
                "field_name": "reminder_message",
                "field_value": "Reminder: '{json.summary}' meeting starts at {json.start.dateTime} ({json.start.timeZone})",
                "field_type": "string"
            },
            {
                "field_name": "event_id",
                "field_value": "{json.id}",
                "field_type": "string"
            }
        ]
        
        # Test with array format (as provided by user)
        result = self.processor.process_input_values(input_values, self.calendar_event_array)
        
        expected = {
            "meeting_title": "hello",
            "meeting_url": "https://www.google.com/calendar/event?eid=*******************************************************************************",
            "organizer": "<EMAIL>",
            "reminder_message": "Reminder: 'hello' meeting starts at 2025-07-25T21:30:00+05:30 (Asia/Kolkata)",
            "event_id": "22r8vjrehp6r8itc8qje2qvl7s"
        }
        
        assert result == expected
