"""
Tests for the TemplateProcessor utility.

This module contains comprehensive tests for template string processing,
including static values, dynamic field references, nested fields,
multiple references, and error handling scenarios.
"""

import pytest
from src.utils.template_processor import TemplateProcessor


class TestTemplateProcessor:
    """Test cases for the TemplateProcessor class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.processor = TemplateProcessor()
        self.sample_event_data = {
            "id": 1,
            "username": "johndo<PERSON>",
            "email": "<EMAIL>",
            "isActive": True,
            "department": "finance",
            "priority_level": 5,
            "user": {
                "profile": {
                    "name": "<PERSON>",
                    "email": "<EMAIL>",
                    "settings": {"theme": "dark", "notifications": True},
                },
                "roles": ["admin", "user"],
            },
            "event": {
                "start": {"dateTime": "2024-01-15T10:00:00Z"},
                "attendees": [
                    {"email": "<EMAIL>"},
                    {"email": "<EMAIL>"},
                ],
            },
        }

    def test_is_template_detection(self):
        """Test template pattern detection."""
        # Template strings
        assert self.processor.is_template("{json.username}")
        assert self.processor.is_template("Hello {json.username}")
        assert self.processor.is_template("{json.user.profile.name}")
        assert self.processor.is_template("User {json.username} from {json.department}")

        # Non-template strings
        assert not self.processor.is_template("static_value")
        assert not self.processor.is_template("Hello world")
        assert not self.processor.is_template("")
        assert not self.processor.is_template(123)
        assert not self.processor.is_template(None)
        assert not self.processor.is_template(True)

    def test_extract_field_references(self):
        """Test field reference extraction."""
        # Single field
        refs = self.processor.extract_field_references("{json.username}")
        assert refs == {"username"}

        # Multiple fields
        refs = self.processor.extract_field_references(
            "User {json.username} from {json.department}"
        )
        assert refs == {"username", "department"}

        # Nested fields
        refs = self.processor.extract_field_references("{json.user.profile.name}")
        assert refs == {"user.profile.name"}

        # Mixed references
        refs = self.processor.extract_field_references(
            "Hello {json.username}, your email is {json.user.profile.email}"
        )
        assert refs == {"username", "user.profile.email"}

        # No references
        refs = self.processor.extract_field_references("static text")
        assert refs == set()

    def test_process_template_single_field(self):
        """Test processing single field references."""
        # Simple field
        result = self.processor.process_template(
            "{json.username}", self.sample_event_data
        )
        assert result == "johndoe"

        # Nested field
        result = self.processor.process_template(
            "{json.user.profile.name}", self.sample_event_data
        )
        assert result == "John Doe"

        # Deep nested field
        result = self.processor.process_template(
            "{json.user.profile.settings.theme}", self.sample_event_data
        )
        assert result == "dark"

        # Non-existent field returns None for single field reference
        result = self.processor.process_template(
            "{json.nonexistent}", self.sample_event_data
        )
        assert result is None

    def test_process_template_multiple_fields(self):
        """Test processing multiple field references in one template."""
        # Multiple fields
        result = self.processor.process_template(
            "User {json.username} from {json.department}", self.sample_event_data
        )
        assert result == "User johndoe from finance"

        # Mixed with nested fields
        result = self.processor.process_template(
            "Hello {json.user.profile.name}, your username is {json.username}",
            self.sample_event_data,
        )
        assert result == "Hello John Doe, your username is johndoe"

        # With non-existent fields (replaced with empty string)
        result = self.processor.process_template(
            "User {json.username} from {json.nonexistent_dept}", self.sample_event_data
        )
        assert result == "User johndoe from "

    def test_process_template_static_values(self):
        """Test processing static (non-template) values."""
        # String
        result = self.processor.process_template("static_value", self.sample_event_data)
        assert result == "static_value"

        # Number
        result = self.processor.process_template(123, self.sample_event_data)
        assert result == 123

        # Boolean
        result = self.processor.process_template(True, self.sample_event_data)
        assert result is True

    def test_process_template_with_field_types(self):
        """Test field type conversion."""
        # String type
        result = self.processor.process_template(
            "{json.username}", self.sample_event_data, "string"
        )
        assert result == "johndoe"
        assert isinstance(result, str)

        # Number type
        result = self.processor.process_template(
            "{json.priority_level}", self.sample_event_data, "number"
        )
        assert result == 5
        assert isinstance(result, int)

        # Boolean type
        result = self.processor.process_template(
            "{json.isActive}", self.sample_event_data, "boolean"
        )
        assert result is True
        assert isinstance(result, bool)

        # Array type for string input
        result = self.processor.process_template(
            "item1,item2,item3", self.sample_event_data, "array"
        )
        assert result == ["item1", "item2", "item3"]

    def test_process_input_values_static(self):
        """Test processing static input values (backward compatibility)."""
        input_values = [
            {
                "field_name": "department",
                "field_value": "finance",
                "field_type": "string",
            },
            {"field_name": "priority_level", "field_value": 5, "field_type": "number"},
            {"field_name": "is_active", "field_value": True, "field_type": "boolean"},
        ]

        result = self.processor.process_input_values(
            input_values, self.sample_event_data
        )

        assert result == {
            "department": "finance",
            "priority_level": 5,
            "is_active": True,
        }

    def test_process_input_values_templates(self):
        """Test processing template input values."""
        input_values = [
            {
                "field_name": "user_name",
                "field_value": "{json.username}",
                "field_type": "string",
            },
            {
                "field_name": "greeting",
                "field_value": "Hello {json.user.profile.name}",
                "field_type": "string",
            },
            {
                "field_name": "user_id",
                "field_value": "{json.id}",
                "field_type": "number",
            },
            {
                "field_name": "status_message",
                "field_value": "User {json.username} from {json.department} is active: {json.isActive}",
                "field_type": "string",
            },
        ]

        result = self.processor.process_input_values(
            input_values, self.sample_event_data
        )

        assert result == {
            "user_name": "johndoe",
            "greeting": "Hello John Doe",
            "user_id": 1,
            "status_message": "User johndoe from finance is active: True",
        }

    def test_process_input_values_mixed(self):
        """Test processing mixed static and template input values."""
        input_values = [
            {
                "field_name": "static_dept",
                "field_value": "engineering",  # Static
                "field_type": "string",
            },
            {
                "field_name": "dynamic_user",
                "field_value": "{json.username}",  # Template
                "field_type": "string",
            },
            {
                "field_name": "mixed_message",
                "field_value": "Welcome {json.user.profile.name} to the team!",  # Mixed
                "field_type": "string",
            },
        ]

        result = self.processor.process_input_values(
            input_values, self.sample_event_data
        )

        assert result == {
            "static_dept": "engineering",
            "dynamic_user": "johndoe",
            "mixed_message": "Welcome John Doe to the team!",
        }

    def test_nested_field_access(self):
        """Test accessing deeply nested fields."""
        # Deep nesting
        result = self.processor.process_template(
            "{json.user.profile.settings.notifications}", self.sample_event_data
        )
        assert result is True

        # Array access (note: this would need special handling for array indices)
        result = self.processor.process_template(
            "{json.user.roles}", self.sample_event_data
        )
        assert result == ["admin", "user"]

    def test_error_handling(self):
        """Test error handling scenarios."""
        # Missing field in single reference returns None
        result = self.processor.process_template(
            "{json.missing_field}", self.sample_event_data
        )
        assert result is None

        # Missing field in multi-field template returns empty string substitution
        result = self.processor.process_template(
            "User {json.username} works in {json.missing_dept}", self.sample_event_data
        )
        assert result == "User johndoe works in "

        # Invalid nested path
        result = self.processor.process_template(
            "{json.user.invalid.path}", self.sample_event_data
        )
        assert result is None

        # Empty input values
        result = self.processor.process_input_values([], self.sample_event_data)
        assert result == {}

        # None input values
        result = self.processor.process_input_values(None, self.sample_event_data)
        assert result == {}

    def test_edge_cases(self):
        """Test edge cases and boundary conditions."""
        # Empty event data
        result = self.processor.process_template("{json.username}", {})
        assert result is None

        # None event data
        result = self.processor.process_template("{json.username}", None)
        assert result is None

        # Malformed template (missing closing brace)
        result = self.processor.process_template(
            "{json.username", self.sample_event_data
        )
        assert result == "{json.username"  # Treated as static text

        # Empty template
        result = self.processor.process_template("", self.sample_event_data)
        assert result == ""

        # Template with only spaces
        result = self.processor.process_template("   ", self.sample_event_data)
        assert result == "   "

    def test_type_conversion_edge_cases(self):
        """Test type conversion edge cases."""
        # String to number conversion
        result = self.processor.process_template(
            "123", self.sample_event_data, "number"
        )
        assert result == 123

        # String to boolean conversion
        result = self.processor.process_template(
            "true", self.sample_event_data, "boolean"
        )
        assert result is True

        result = self.processor.process_template(
            "false", self.sample_event_data, "boolean"
        )
        assert result is False

        # Invalid number conversion (should return original)
        result = self.processor.process_template(
            "not_a_number", self.sample_event_data, "number"
        )
        assert result == "not_a_number"


class TestTemplateProcessorIntegration:
    """Integration tests for template processor with workflow executor scenarios."""

    def setup_method(self):
        """Set up test fixtures."""
        self.processor = TemplateProcessor()

    def test_google_calendar_event_processing(self):
        """Test processing Google Calendar event data."""
        calendar_event_data = {
            "summary": "Team Meeting",
            "start": {"dateTime": "2024-01-15T10:00:00Z"},
            "end": {"dateTime": "2024-01-15T11:00:00Z"},
            "attendees": [
                {"email": "<EMAIL>", "displayName": "User One"},
                {"email": "<EMAIL>", "displayName": "User Two"},
            ],
            "creator": {"email": "<EMAIL>", "displayName": "Event Creator"},
        }

        input_values = [
            {
                "field_name": "event_title",
                "field_value": "{json.summary}",
                "field_type": "string",
            },
            {
                "field_name": "start_time",
                "field_value": "{json.start.dateTime}",
                "field_type": "string",
            },
            {
                "field_name": "creator_email",
                "field_value": "{json.creator.email}",
                "field_type": "string",
            },
            {
                "field_name": "notification_message",
                "field_value": "Meeting '{json.summary}' starts at {json.start.dateTime}",
                "field_type": "string",
            },
        ]

        result = self.processor.process_input_values(input_values, calendar_event_data)

        assert result == {
            "event_title": "Team Meeting",
            "start_time": "2024-01-15T10:00:00Z",
            "creator_email": "<EMAIL>",
            "notification_message": "Meeting 'Team Meeting' starts at 2024-01-15T10:00:00Z",
        }

    def test_google_drive_file_processing(self):
        """Test processing Google Drive file event data."""
        drive_file_data = {
            "name": "Project Report.pdf",
            "mimeType": "application/pdf",
            "size": "1048576",
            "owners": [{"displayName": "John Doe", "emailAddress": "<EMAIL>"}],
            "modifiedTime": "2024-01-15T10:00:00Z",
            "webViewLink": "https://drive.google.com/file/d/abc123/view",
        }

        input_values = [
            {
                "field_name": "file_name",
                "field_value": "{json.name}",
                "field_type": "string",
            },
            {
                "field_name": "file_size_mb",
                "field_value": "{json.size}",
                "field_type": "number",
            },
            {
                "field_name": "owner_email",
                "field_value": "{json.owners.0.emailAddress}",  # Note: array access would need special handling
                "field_type": "string",
            },
            {
                "field_name": "summary",
                "field_value": "File '{json.name}' was modified by {json.owners.0.displayName}",
                "field_type": "string",
            },
        ]

        result = self.processor.process_input_values(input_values, drive_file_data)

        # Note: Array access like owners.0.emailAddress would return None with current implementation
        # This is expected behavior - array indexing would need special handling if required
        assert result["file_name"] == "Project Report.pdf"
        assert result["file_size_mb"] == 1048576  # Converted to number
        assert result["owner_email"] is None  # Array indexing not supported
        assert "File 'Project Report.pdf' was modified by" in result["summary"]

    def test_scheduler_event_processing(self):
        """Test processing scheduler event data."""
        scheduler_event_data = {
            "trigger_type": "scheduler",
            "execution_time": "2024-01-15T10:00:00Z",
            "scheduler_id": "sched_123",
            "user_context": {
                "user_id": "user_456",
                "department": "engineering",
                "role": "developer",
            },
        }

        input_values = [
            {
                "field_name": "execution_timestamp",
                "field_value": "{json.execution_time}",
                "field_type": "string",
            },
            {
                "field_name": "user_department",
                "field_value": "{json.user_context.department}",
                "field_type": "string",
            },
            {
                "field_name": "notification",
                "field_value": "Scheduled task executed at {json.execution_time} for {json.user_context.department}",
                "field_type": "string",
            },
        ]

        result = self.processor.process_input_values(input_values, scheduler_event_data)

        assert result == {
            "execution_timestamp": "2024-01-15T10:00:00Z",
            "user_department": "engineering",
            "notification": "Scheduled task executed at 2024-01-15T10:00:00Z for engineering",
        }

    def test_complex_nested_data_processing(self):
        """Test processing complex nested data structures."""
        complex_event_data = {
            "event": {
                "type": "user_action",
                "user": {
                    "id": 123,
                    "profile": {
                        "personal": {"firstName": "John", "lastName": "Doe"},
                        "professional": {
                            "title": "Senior Developer",
                            "department": "Engineering",
                        },
                    },
                },
                "action": {
                    "type": "file_upload",
                    "details": {
                        "fileName": "document.pdf",
                        "fileSize": 2048,
                        "timestamp": "2024-01-15T10:00:00Z",
                    },
                },
            }
        }

        input_values = [
            {
                "field_name": "full_name",
                "field_value": "{json.event.user.profile.personal.firstName} {json.event.user.profile.personal.lastName}",
                "field_type": "string",
            },
            {
                "field_name": "user_title",
                "field_value": "{json.event.user.profile.professional.title}",
                "field_type": "string",
            },
            {
                "field_name": "action_summary",
                "field_value": "{json.event.user.profile.personal.firstName} uploaded {json.event.action.details.fileName}",
                "field_type": "string",
            },
            {
                "field_name": "file_size_kb",
                "field_value": "{json.event.action.details.fileSize}",
                "field_type": "number",
            },
        ]

        result = self.processor.process_input_values(input_values, complex_event_data)

        assert result == {
            "full_name": "John Doe",
            "user_title": "Senior Developer",
            "action_summary": "John uploaded document.pdf",
            "file_size_kb": 2048,
        }
