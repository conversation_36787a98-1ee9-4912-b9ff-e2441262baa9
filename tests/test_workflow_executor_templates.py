"""
Tests for template processing integration in WorkflowExecutor.

This module tests the integration of template processing with the workflow
executor, ensuring that input_values with template strings are properly
processed during workflow execution.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from src.core.workflow_executor import WorkflowExecutor


class TestWorkflowExecutorTemplateIntegration:
    """Test template processing integration in WorkflowExecutor."""

    def setup_method(self):
        """Set up test fixtures."""
        self.executor = WorkflowExecutor()

    def test_extract_scheduler_input_values_with_templates(self):
        """Test scheduler input values processing with templates."""
        input_values = [
            {
                "field_name": "user_name",
                "field_value": "{json.username}",
                "field_type": "string"
            },
            {
                "field_name": "greeting",
                "field_value": "Hello {json.user.profile.name}",
                "field_type": "string"
            },
            {
                "field_name": "static_value",
                "field_value": "finance",
                "field_type": "string"
            }
        ]

        event_data = {
            "username": "johndo<PERSON>",
            "user": {
                "profile": {
                    "name": "<PERSON>"
                }
            }
        }

        result = self.executor._extract_scheduler_input_values(input_values, event_data)

        assert result == {
            "user_name": "johndoe",
            "greeting": "Hello <PERSON> Doe",
            "static_value": "finance"
        }

    def test_extract_scheduler_input_values_without_event_data(self):
        """Test scheduler input values processing without event data (backward compatibility)."""
        input_values = [
            {
                "field_name": "department",
                "field_value": "finance",
                "field_type": "string"
            },
            {
                "field_name": "priority",
                "field_value": 5,
                "field_type": "number"
            }
        ]

        # No event data provided - should use static processing
        result = self.executor._extract_scheduler_input_values(input_values)

        assert result == {
            "department": "finance",
            "priority": 5
        }

    def test_transform_event_to_payload_scheduler_with_templates(self):
        """Test event transformation with scheduler input values containing templates."""
        event_data = {
            "scheduler_input_values": [
                {
                    "field_name": "user_name",
                    "field_value": "{json.username}",
                    "field_type": "string"
                },
                {
                    "field_name": "notification",
                    "field_value": "Hello {json.username}, your task is ready!",
                    "field_type": "string"
                }
            ],
            "username": "johndoe",
            "department": "engineering"
        }

        workflow_data = {
            "start_nodes": [
                {
                    "field": "user_name",
                    "type": "string",
                    "transition_id": "transition-1"
                },
                {
                    "field": "notification",
                    "type": "string",
                    "transition_id": "transition-2"
                }
            ]
        }

        result = self.executor._transform_event_to_payload(event_data, workflow_data)

        expected_payload = {
            "user_dependent_fields": ["user_name", "notification"],
            "user_payload_template": {
                "user_name": {
                    "transition_id": "transition-1",
                    "value": "johndoe"
                },
                "notification": {
                    "transition_id": "transition-2",
                    "value": "Hello johndoe, your task is ready!"
                }
            }
        }

        assert result == expected_payload

    def test_transform_event_to_payload_trigger_with_input_values(self):
        """Test event transformation with trigger input values containing templates."""
        event_data = {
            "trigger_config": {
                "input_values": [
                    {
                        "field_name": "event_title",
                        "field_value": "{json.summary}",
                        "field_type": "string"
                    },
                    {
                        "field_name": "start_time",
                        "field_value": "{json.start.dateTime}",
                        "field_type": "string"
                    },
                    {
                        "field_name": "notification",
                        "field_value": "Meeting '{json.summary}' starts at {json.start.dateTime}",
                        "field_type": "string"
                    }
                ]
            },
            "summary": "Team Meeting",
            "start": {
                "dateTime": "2024-01-15T10:00:00Z"
            }
        }

        workflow_data = {
            "start_nodes": [
                {
                    "field": "event_title",
                    "type": "string",
                    "transition_id": "transition-1"
                },
                {
                    "field": "notification",
                    "type": "string",
                    "transition_id": "transition-2"
                }
            ]
        }

        result = self.executor._transform_event_to_payload(event_data, workflow_data)

        expected_payload = {
            "user_dependent_fields": ["event_title", "notification"],
            "user_payload_template": {
                "event_title": {
                    "transition_id": "transition-1",
                    "value": "Team Meeting"
                },
                "notification": {
                    "transition_id": "transition-2",
                    "value": "Meeting 'Team Meeting' starts at 2024-01-15T10:00:00Z"
                }
            }
        }

        assert result == expected_payload

    def test_transform_event_to_payload_fallback_to_selected_fields(self):
        """Test fallback to selected event fields when no input_values."""
        event_data = {
            "trigger_config": {
                "selected_event_fields": [
                    {
                        "calendar_field": "summary",
                        "workflow_field": "event_title",
                        "field_type": "string"
                    }
                ]
            },
            "data": {
                "summary": "Team Meeting"
            }
        }

        workflow_data = {
            "start_nodes": [
                {
                    "field": "event_title",
                    "type": "string",
                    "transition_id": "transition-1"
                }
            ]
        }

        result = self.executor._transform_event_to_payload(event_data, workflow_data)

        expected_payload = {
            "user_dependent_fields": ["event_title"],
            "user_payload_template": {
                "event_title": {
                    "transition_id": "transition-1",
                    "value": "Team Meeting"
                }
            }
        }

        assert result == expected_payload

    def test_transform_event_to_payload_missing_template_fields(self):
        """Test handling of missing fields in template processing."""
        event_data = {
            "scheduler_input_values": [
                {
                    "field_name": "user_name",
                    "field_value": "{json.username}",
                    "field_type": "string"
                },
                {
                    "field_name": "missing_field_ref",
                    "field_value": "{json.nonexistent}",
                    "field_type": "string"
                },
                {
                    "field_name": "mixed_template",
                    "field_value": "User {json.username} from {json.missing_dept}",
                    "field_type": "string"
                }
            ],
            "username": "johndoe"
            # Note: missing_dept is not provided
        }

        workflow_data = {
            "start_nodes": [
                {
                    "field": "user_name",
                    "type": "string",
                    "transition_id": "transition-1"
                },
                {
                    "field": "missing_field_ref",
                    "type": "string",
                    "transition_id": "transition-2"
                },
                {
                    "field": "mixed_template",
                    "type": "string",
                    "transition_id": "transition-3"
                }
            ]
        }

        result = self.executor._transform_event_to_payload(event_data, workflow_data)

        expected_payload = {
            "user_dependent_fields": ["user_name", "missing_field_ref", "mixed_template"],
            "user_payload_template": {
                "user_name": {
                    "transition_id": "transition-1",
                    "value": "johndoe"
                },
                "missing_field_ref": {
                    "transition_id": "transition-2",
                    "value": None  # Single field reference to missing field returns None
                },
                "mixed_template": {
                    "transition_id": "transition-3",
                    "value": "User johndoe from "  # Missing field replaced with empty string
                }
            }
        }

        assert result == expected_payload

    def test_complex_nested_template_processing(self):
        """Test complex nested field template processing."""
        event_data = {
            "scheduler_input_values": [
                {
                    "field_name": "user_full_name",
                    "field_value": "{json.user.profile.personal.firstName} {json.user.profile.personal.lastName}",
                    "field_type": "string"
                },
                {
                    "field_name": "department_title",
                    "field_value": "{json.user.profile.professional.title} in {json.user.profile.professional.department}",
                    "field_type": "string"
                }
            ],
            "user": {
                "profile": {
                    "personal": {
                        "firstName": "John",
                        "lastName": "Doe"
                    },
                    "professional": {
                        "title": "Senior Developer",
                        "department": "Engineering"
                    }
                }
            }
        }

        workflow_data = {
            "start_nodes": [
                {
                    "field": "user_full_name",
                    "type": "string",
                    "transition_id": "transition-1"
                },
                {
                    "field": "department_title",
                    "type": "string",
                    "transition_id": "transition-2"
                }
            ]
        }

        result = self.executor._transform_event_to_payload(event_data, workflow_data)

        expected_payload = {
            "user_dependent_fields": ["user_full_name", "department_title"],
            "user_payload_template": {
                "user_full_name": {
                    "transition_id": "transition-1",
                    "value": "John Doe"
                },
                "department_title": {
                    "transition_id": "transition-2",
                    "value": "Senior Developer in Engineering"
                }
            }
        }

        assert result == expected_payload
