"""
Tests for the sample event data endpoint.

This module tests the new endpoint that provides sample event data
for different trigger types to help with configuration.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient
from src.main import app


class TestSampleEventEndpoint:
    """Test the sample event data endpoint."""

    def setup_method(self):
        """Set up test fixtures."""
        self.client = TestClient(app)
        self.test_user_id = "test_user_123"
        self.auth_headers = {"Authorization": f"Bearer test_token"}

    @patch("src.api.routes.triggers.get_authenticated_user")
    @patch("src.api.routes.triggers.get_trigger_manager")
    def test_get_static_sample_data_google_calendar(self, mock_trigger_manager, mock_auth):
        """Test getting static sample data for Google Calendar."""
        # Mock authentication
        mock_auth.return_value = self.test_user_id
        
        # Mock trigger manager and adapter
        mock_adapter = MagicMock()
        mock_adapter.get_adapter_info.return_value = {
            "trigger_type": "google_calendar",
            "name": "Google Calendar",
            "description": "Google Calendar trigger adapter",
            "supported_event_types": ["created", "updated", "deleted"],
            "configuration_schema": {},
            "sample_event_data": {
                "kind": "calendar#event",
                "id": "sample-event-id",
                "summary": "Sample Meeting",
                "start": {"dateTime": "2024-01-15T10:00:00Z"},
                "creator": {"email": "<EMAIL>"},
                "eventType": "default"
            },
            "available_fields": ["summary", "start.dateTime", "creator.email", "eventType"]
        }
        
        mock_manager = MagicMock()
        mock_manager.get_adapter.return_value = mock_adapter
        mock_trigger_manager.return_value = mock_manager
        
        # Make request
        response = self.client.get(
            "/api/v1/triggers/sample-event/google_calendar",
            headers=self.auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        assert data["trigger_type"] == "google_calendar"
        assert data["is_live_data"] is False
        assert data["data_source"] == "static"
        assert "event_data" in data
        assert data["event_data"]["summary"] == "Sample Meeting"
        assert "template_examples" in data
        assert len(data["template_examples"]) > 0
        assert data["template_examples"][0]["field_value"] == "{json.summary}"

    @patch("src.api.routes.triggers.get_authenticated_user")
    @patch("src.api.routes.triggers.get_trigger_manager")
    def test_get_sample_data_invalid_trigger_type(self, mock_trigger_manager, mock_auth):
        """Test getting sample data for invalid trigger type."""
        # Mock authentication
        mock_auth.return_value = self.test_user_id
        
        # Mock trigger manager returning None for invalid adapter
        mock_manager = MagicMock()
        mock_manager.get_adapter.return_value = None
        mock_trigger_manager.return_value = mock_manager
        
        # Make request
        response = self.client.get(
            "/api/v1/triggers/sample-event/invalid_trigger",
            headers=self.auth_headers
        )
        
        # Assertions
        assert response.status_code == 404
        assert "not found or not supported" in response.json()["detail"]

    @patch("src.api.routes.triggers.get_authenticated_user")
    @patch("src.api.routes.triggers.get_trigger_manager")
    @patch("src.api.routes.triggers._fetch_live_sample_data")
    def test_get_live_sample_data_success(self, mock_fetch_live, mock_trigger_manager, mock_auth):
        """Test getting live sample data successfully."""
        # Mock authentication
        mock_auth.return_value = self.test_user_id
        
        # Mock trigger manager and adapter
        mock_adapter = MagicMock()
        mock_adapter.get_adapter_info.return_value = {
            "trigger_type": "google_calendar",
            "sample_event_data": {},
            "available_fields": ["summary", "start.dateTime"],
            "configuration_schema": {}
        }
        
        mock_manager = MagicMock()
        mock_manager.get_adapter.return_value = mock_adapter
        mock_trigger_manager.return_value = mock_manager
        
        # Mock live data fetch
        live_event_data = {
            "kind": "calendar#event",
            "id": "live-event-123",
            "summary": "Real Meeting",
            "start": {"dateTime": "2024-01-15T14:00:00Z"},
            "creator": {"email": "<EMAIL>"},
            "eventType": "default"
        }
        mock_fetch_live.return_value = live_event_data
        
        # Make request with live=true
        response = self.client.get(
            "/api/v1/triggers/sample-event/google_calendar?live=true",
            headers=self.auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        assert data["trigger_type"] == "google_calendar"
        assert data["is_live_data"] is True
        assert data["data_source"] == "live"
        assert data["event_data"]["summary"] == "Real Meeting"
        assert data["event_data"]["id"] == "live-event-123"

    @patch("src.api.routes.triggers.get_authenticated_user")
    @patch("src.api.routes.triggers.get_trigger_manager")
    @patch("src.api.routes.triggers._fetch_live_sample_data")
    def test_get_live_sample_data_fallback(self, mock_fetch_live, mock_trigger_manager, mock_auth):
        """Test live sample data falling back to static when live fetch fails."""
        # Mock authentication
        mock_auth.return_value = self.test_user_id
        
        # Mock trigger manager and adapter
        static_sample = {
            "kind": "calendar#event",
            "id": "static-sample",
            "summary": "Static Sample Meeting"
        }
        
        mock_adapter = MagicMock()
        mock_adapter.get_adapter_info.return_value = {
            "trigger_type": "google_calendar",
            "sample_event_data": static_sample,
            "available_fields": ["summary"],
            "configuration_schema": {}
        }
        
        mock_manager = MagicMock()
        mock_manager.get_adapter.return_value = mock_adapter
        mock_trigger_manager.return_value = mock_manager
        
        # Mock live data fetch returning None (failure)
        mock_fetch_live.return_value = None
        
        # Make request with live=true
        response = self.client.get(
            "/api/v1/triggers/sample-event/google_calendar?live=true",
            headers=self.auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        assert data["trigger_type"] == "google_calendar"
        assert data["is_live_data"] is True
        assert data["data_source"] == "static_fallback"
        assert data["event_data"]["summary"] == "Static Sample Meeting"
        assert "live_data_note" in data

    def test_get_sample_data_unauthorized(self):
        """Test getting sample data without authentication."""
        response = self.client.get("/api/v1/triggers/sample-event/google_calendar")
        
        # Should return 401 or 403 depending on auth middleware
        assert response.status_code in [401, 403]

    @patch("src.api.routes.triggers.get_authenticated_user")
    @patch("src.api.routes.triggers.get_trigger_manager")
    def test_template_examples_included(self, mock_trigger_manager, mock_auth):
        """Test that template examples are included in the response."""
        # Mock authentication
        mock_auth.return_value = self.test_user_id
        
        # Mock trigger manager and adapter
        mock_adapter = MagicMock()
        mock_adapter.get_adapter_info.return_value = {
            "trigger_type": "google_calendar",
            "sample_event_data": {"summary": "Test Event"},
            "available_fields": ["summary", "start.dateTime"],
            "configuration_schema": {}
        }
        
        mock_manager = MagicMock()
        mock_manager.get_adapter.return_value = mock_adapter
        mock_trigger_manager.return_value = mock_manager
        
        # Make request
        response = self.client.get(
            "/api/v1/triggers/sample-event/google_calendar",
            headers=self.auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        assert "template_examples" in data
        examples = data["template_examples"]
        assert len(examples) > 0
        
        # Check that examples have the expected structure
        for example in examples:
            assert "field_name" in example
            assert "field_value" in example
            assert "field_type" in example
            assert "description" in example
            assert example["field_value"].startswith("{json.")

    @patch("src.api.routes.triggers.get_authenticated_user")
    @patch("src.api.routes.triggers.get_trigger_manager")
    def test_scheduler_sample_data(self, mock_trigger_manager, mock_auth):
        """Test getting sample data for scheduler trigger type."""
        # Mock authentication
        mock_auth.return_value = self.test_user_id
        
        # Mock trigger manager and adapter for scheduler
        mock_adapter = MagicMock()
        mock_adapter.get_adapter_info.return_value = {
            "trigger_type": "scheduler",
            "sample_event_data": {
                "trigger_type": "scheduler",
                "execution_time": "2024-01-15T09:00:00Z"
            },
            "available_fields": ["execution_time", "user_context.department"],
            "configuration_schema": {}
        }
        
        mock_manager = MagicMock()
        mock_manager.get_adapter.return_value = mock_adapter
        mock_trigger_manager.return_value = mock_manager
        
        # Make request
        response = self.client.get(
            "/api/v1/triggers/sample-event/scheduler",
            headers=self.auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        assert data["trigger_type"] == "scheduler"
        assert "template_examples" in data
        
        # Check scheduler-specific template examples
        examples = data["template_examples"]
        field_names = [ex["field_name"] for ex in examples]
        assert "execution_time" in field_names
        assert "user_department" in field_names
