# Sample Event Data Endpoint

## Overview

The Sample Event Data endpoint (`/api/v1/triggers/sample-event/{trigger_type}`) provides sample event data for different trigger types to help users configure triggers and workflows. This endpoint can return either static sample data or live data from the user's connected services.

## Endpoint Details

**URL**: `GET /api/v1/triggers/sample-event/{trigger_type}`

**Authentication**: Bearer token or API key required

**Path Parameters**:
- `trigger_type`: The type of trigger (e.g., `google_calendar`, `google_drive`, `scheduler`)

**Query Parameters**:
- `live` (boolean, optional): Set to `true` to fetch actual recent event data (requires OAuth). Default: `false`
- `calendar_id` (string, optional): For Google Calendar, specify calendar ID. Default: `"primary"`

## Supported Trigger Types

### Google Calendar (`google_calendar`)

**Static Sample Data**: Returns a pre-defined Google Calendar event structure
**Live Data**: Fetches a recent event from the user's Google Calendar (requires OAuth)

**Example Response**:
```json
{
  "trigger_type": "google_calendar",
  "is_live_data": false,
  "data_source": "static",
  "event_data": {
    "kind": "calendar#event",
    "id": "sample-event-id",
    "summary": "Sample Meeting",
    "start": {
      "dateTime": "2024-01-15T10:00:00Z",
      "timeZone": "America/Los_Angeles"
    },
    "creator": {
      "email": "<EMAIL>"
    },
    "eventType": "default"
  },
  "available_fields": [
    "summary", "start.dateTime", "creator.email", "eventType"
  ],
  "template_examples": [
    {
      "field_name": "event_title",
      "field_value": "{json.summary}",
      "field_type": "string",
      "description": "Event title/summary"
    },
    {
      "field_name": "start_time",
      "field_value": "{json.start.dateTime}",
      "field_type": "string",
      "description": "Event start date and time"
    }
  ],
  "configuration_schema": {
    "type": "object",
    "properties": {
      "calendar_id": {
        "type": "string",
        "description": "Google Calendar ID"
      }
    }
  }
}
```

### Google Drive (`google_drive`)

**Static Sample Data**: Returns a pre-defined Google Drive file event structure
**Live Data**: Not yet implemented (falls back to static)

### Scheduler (`scheduler`)

**Static Sample Data**: Returns a pre-defined scheduler event structure
**Live Data**: Returns a dynamic sample with current timestamp

## Usage Examples

### Get Static Sample Data

```bash
curl -X GET "http://localhost:8000/api/v1/triggers/sample-event/google_calendar" \
     -H "Authorization: Bearer your_token_here"
```

### Get Live Sample Data

```bash
curl -X GET "http://localhost:8000/api/v1/triggers/sample-event/google_calendar?live=true" \
     -H "Authorization: Bearer your_token_here"
```

### Get Live Data from Specific Calendar

```bash
curl -X GET "http://localhost:8000/api/v1/triggers/sample-event/google_calendar?live=true&calendar_id=your_calendar_id" \
     -H "Authorization: Bearer your_token_here"
```

## Response Structure

All responses include the following fields:

- `trigger_type`: The type of trigger requested
- `is_live_data`: Boolean indicating if live data was requested
- `data_source`: Source of the data (`"static"`, `"live"`, or `"static_fallback"`)
- `event_data`: The actual event data structure
- `available_fields`: List of available fields for template processing
- `template_examples`: Example template configurations
- `configuration_schema`: JSON schema for trigger configuration

## Template Examples

The endpoint provides ready-to-use template examples for each trigger type:

### Google Calendar Templates

```json
[
  {
    "field_name": "event_title",
    "field_value": "{json.summary}",
    "field_type": "string",
    "description": "Event title/summary"
  },
  {
    "field_name": "notification_message",
    "field_value": "Meeting '{json.summary}' starts at {json.start.dateTime}",
    "field_type": "string",
    "description": "Mixed template with multiple fields"
  }
]
```

### Google Drive Templates

```json
[
  {
    "field_name": "file_name",
    "field_value": "{json.name}",
    "field_type": "string",
    "description": "Name of the file"
  },
  {
    "field_name": "file_summary",
    "field_value": "File '{json.name}' was modified by {json.owners.0.displayName}",
    "field_type": "string",
    "description": "Mixed template with file information"
  }
]
```

## Error Handling

### 404 - Trigger Type Not Found
```json
{
  "detail": "Trigger type 'invalid_type' not found or not supported"
}
```

### 401 - Authentication Required
```json
{
  "detail": "Authentication required"
}
```

### Live Data Fallback
When live data cannot be fetched, the endpoint automatically falls back to static data:

```json
{
  "trigger_type": "google_calendar",
  "is_live_data": true,
  "data_source": "static_fallback",
  "live_data_note": "Live data not available, showing static sample. Ensure OAuth is completed for your account.",
  "event_data": { /* static sample data */ }
}
```

## Integration with Workflow Configuration

Use the sample event data to:

1. **Understand Event Structure**: See what fields are available in real events
2. **Configure Templates**: Use the `template_examples` to set up input value templates
3. **Test Workflows**: Use the sample data to test workflow configurations before going live
4. **Field Validation**: Ensure your templates reference valid fields from `available_fields`

## Live Data Requirements

To fetch live data:

1. **OAuth Completion**: User must have completed OAuth flow for the service
2. **Valid Credentials**: OAuth credentials must be valid and not expired
3. **Service Access**: User must have access to the requested resources (e.g., calendar)
4. **Recent Data**: For some services, recent activity is required to fetch sample events

## Best Practices

1. **Start with Static Data**: Use static samples during initial development
2. **Test with Live Data**: Use live data to validate templates with real event structures
3. **Handle Fallbacks**: Always handle cases where live data might not be available
4. **Cache Results**: Consider caching sample data for better performance
5. **Validate Templates**: Test template expressions against the sample data structure
