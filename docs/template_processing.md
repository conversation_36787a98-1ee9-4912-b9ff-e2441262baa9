# Template Processing for Dynamic Input Values

This document describes the template processing functionality that allows dynamic substitution of event data in input values for both schedulers and triggers.

## Overview

The template processing system enables users to create dynamic input values that reference fields from trigger event data using a simple `{json.field}` syntax. This allows for flexible workflow execution where input parameters can be dynamically populated based on the actual event data.

## Features

- **Static Values**: Traditional static values continue to work unchanged (backward compatible)
- **Dynamic Field References**: Reference event data fields using `{json.field}` syntax
- **Nested Field Access**: Access deeply nested fields using dot notation `{json.user.profile.name}`
- **Multiple References**: Use multiple field references in a single template string
- **Mixed Content**: Combine static text with dynamic field references
- **Type Conversion**: Automatic type conversion based on field_type specification
- **Error Handling**: Graceful handling of missing fields
- **Flexible Event Data**: Handles both object `{...}` and array `[{...}]` event data formats

## Syntax

### Basic Template Syntax

Templates use the `{json.field}` syntax where:

- `json` is a required prefix indicating event data
- `field` is the field path using dot notation for nested access

### Examples

```json
{
  "field_name": "user_name",
  "field_value": "{json.username}",
  "field_type": "string"
}
```

```json
{
  "field_name": "greeting",
  "field_value": "Hello {json.user.profile.name}",
  "field_type": "string"
}
```

```json
{
  "field_name": "notification",
  "field_value": "User {json.username} from {json.department} logged in",
  "field_type": "string"
}
```

## Usage in Schedulers

Schedulers can use template processing in their `input_values` configuration:

```json
{
  "name": "User Notification Scheduler",
  "frequency": "daily",
  "time": "09:00",
  "timezone": "UTC",
  "workflow_id": "wf_notifications",
  "input_values": [
    {
      "field_name": "user_name",
      "field_value": "{json.username}",
      "field_type": "string"
    },
    {
      "field_name": "department",
      "field_value": "finance",
      "field_type": "string"
    },
    {
      "field_name": "welcome_message",
      "field_value": "Welcome {json.username} to the {json.department} team!",
      "field_type": "string"
    }
  ]
}
```

## Usage in Triggers

Triggers can include `input_values` in their `trigger_config` for template processing:

```json
{
  "workflow_id": "workflow-456",
  "trigger_type": "google_calendar",
  "trigger_name": "Dynamic Event Processing",
  "trigger_config": {
    "calendar_id": "primary",
    "use_polling": false,
    "webhook_ttl": 604800,
    "input_values": [
      {
        "field_name": "event_title",
        "field_value": "{json.summary}",
        "field_type": "string"
      },
      {
        "field_name": "start_time",
        "field_value": "{json.start.dateTime}",
        "field_type": "string"
      },
      {
        "field_name": "notification_message",
        "field_value": "Meeting '{json.summary}' starts at {json.start.dateTime}",
        "field_type": "string"
      }
    ]
  },
  "event_types": ["created", "updated"]
}
```

## Field Types and Conversion

The system supports automatic type conversion based on the `field_type` specification:

### String Type

```json
{
  "field_name": "user_name",
  "field_value": "{json.username}",
  "field_type": "string"
}
```

### Number Type

```json
{
  "field_name": "priority_level",
  "field_value": "{json.priority}",
  "field_type": "number"
}
```

### Boolean Type

```json
{
  "field_name": "is_active",
  "field_value": "{json.active}",
  "field_type": "boolean"
}
```

### Array Type

```json
{
  "field_name": "tags",
  "field_value": "tag1,tag2,tag3",
  "field_type": "array"
}
```

## Nested Field Access

Access nested fields using dot notation:

```json
{
  "field_name": "user_email",
  "field_value": "{json.user.profile.email}",
  "field_type": "string"
}
```

```json
{
  "field_name": "event_start",
  "field_value": "{json.event.start.dateTime}",
  "field_type": "string"
}
```

## Error Handling

### Missing Fields

- **Single field reference**: Returns `null` if the field doesn't exist

  ```json
  "{json.nonexistent}" → null
  ```

- **Multiple field references**: Missing fields are replaced with empty strings
  ```json
  "User {json.username} from {json.missing_dept}" → "User johndoe from "
  ```

### Invalid Syntax

Malformed template syntax is treated as static text:

```json
"{json.username" → "{json.username" (literal string)
```

## Event Data Formats

The template processing system automatically handles different event data formats:

### Object Format

```json
{
  "summary": "Team Meeting",
  "start": {
    "dateTime": "2024-01-15T10:00:00Z"
  },
  "creator": {
    "email": "<EMAIL>"
  }
}
```

### Array Format

```json
[
  {
    "summary": "Team Meeting",
    "start": {
      "dateTime": "2024-01-15T10:00:00Z"
    },
    "creator": {
      "email": "<EMAIL>"
    }
  }
]
```

Both formats work identically with template processing. When an array is provided, the system automatically uses the first element.

## Event Data Examples

### Google Calendar Event

```json
{
  "summary": "Team Meeting",
  "start": {
    "dateTime": "2024-01-15T10:00:00Z"
  },
  "attendees": [
    { "email": "<EMAIL>" },
    { "email": "<EMAIL>" }
  ],
  "creator": {
    "email": "<EMAIL>",
    "displayName": "Event Creator"
  }
}
```

Template usage:

```json
{
  "field_name": "meeting_info",
  "field_value": "Meeting '{json.summary}' created by {json.creator.displayName}",
  "field_type": "string"
}
```

Result: `"Meeting 'Team Meeting' created by Event Creator"`

### Google Drive File Event

```json
{
  "name": "Project Report.pdf",
  "mimeType": "application/pdf",
  "size": "1048576",
  "owners": [{ "displayName": "John Doe", "emailAddress": "<EMAIL>" }],
  "modifiedTime": "2024-01-15T10:00:00Z"
}
```

Template usage:

```json
{
  "field_name": "file_info",
  "field_value": "File '{json.name}' ({json.mimeType}) was modified",
  "field_type": "string"
}
```

Result: `"File 'Project Report.pdf' (application/pdf) was modified"`

## Backward Compatibility

The template processing system is fully backward compatible:

- Static values continue to work unchanged
- Existing configurations without templates are unaffected
- The system automatically detects template syntax and processes accordingly

## Implementation Details

### Template Detection

The system uses regex pattern matching to detect `{json.field}` syntax in field values.

### Processing Order

1. Check if the value contains template syntax
2. If yes, extract field references and substitute with event data
3. If no, treat as static value (backward compatibility)
4. Apply type conversion based on field_type

### Performance

- Template processing is lightweight and efficient
- Only processes values that contain template syntax
- Caches compiled regex patterns for optimal performance

## Limitations

- Array indexing (e.g., `{json.items.0.name}`) is not currently supported
- Complex expressions or calculations are not supported
- Only supports `json.` prefix for event data access

## Future Enhancements

Potential future improvements could include:

- Array indexing support
- Additional data source prefixes (e.g., `{user.profile}`, `{config.setting}`)
- Simple expression evaluation
- Template validation and syntax highlighting in UI
